"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView } from "react-native"
import { Ionicons } from "@expo/vector-icons"
import type { BillItemParserProps, BillItem } from "../types"

const BillItemParser: React.FC<BillItemParserProps> = ({ items, groupMembers, onItemsChange }) => {
  const [billItems, setBillItems] = useState<BillItem[]>([])

  useEffect(() => {
    const convertedItems: BillItem[] = items.map((item, index) => ({
      id: `temp_${index}`,
      bill_id: "",
      name: item.name,
      price: item.price,
      quantity: item.quantity,
      assigned_users: [],
      created_at: new Date().toISOString(),
    }))
    setBillItems(convertedItems)
  }, [items])

  useEffect(() => {
    onItemsChange(billItems)
  }, [billItems, onItemsChange])

  const updateItem = (index: number, updates: Partial<BillItem>) => {
    setBillItems((prev) => prev.map((item, i) => (i === index ? { ...item, ...updates } : item)))
  }

  const removeItem = (index: number) => {
    setBillItems((prev) => prev.filter((_, i) => i !== index))
  }

  const addItem = () => {
    const newItem: BillItem = {
      id: `temp_${Date.now()}`,
      bill_id: "",
      name: "",
      price: 0,
      quantity: 1,
      assigned_users: [],
      created_at: new Date().toISOString(),
    }
    setBillItems((prev) => [...prev, newItem])
  }

  const toggleUserAssignment = (itemIndex: number, userId: string) => {
    setBillItems((prev) =>
      prev.map((item, i) => {
        if (i === itemIndex) {
          const assignedUsers = item.assigned_users || []
          const isAssigned = assignedUsers.includes(userId)
          return {
            ...item,
            assigned_users: isAssigned ? assignedUsers.filter((id) => id !== userId) : [...assignedUsers, userId],
          }
        }
        return item
      }),
    )
  }

  const renderMemberChips = (itemIndex: number) => (
    <View style={styles.membersList}>
      {groupMembers.map((member) => {
        const isAssigned = (billItems[itemIndex]?.assigned_users || []).includes(member.id)
        return (
          <TouchableOpacity
            key={member.id}
            style={[styles.memberChip, isAssigned && styles.memberChipSelected]}
            onPress={() => toggleUserAssignment(itemIndex, member.id)}
          >
            <Text style={[styles.memberChipText, isAssigned && styles.memberChipTextSelected]}>
              {member.display_name}
            </Text>
          </TouchableOpacity>
        )
      })}
    </View>
  )

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Bill Items</Text>
        <TouchableOpacity onPress={addItem} style={styles.addButton}>
          <Ionicons name="add" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {billItems.map((item, index) => (
        <View key={item.id} style={styles.itemContainer}>
          <View style={styles.itemHeader}>
            <TextInput
              style={styles.nameInput}
              value={item.name}
              onChangeText={(text) => updateItem(index, { name: text })}
              placeholder="Item name"
            />
            <View style={styles.priceQuantityContainer}>
              <TextInput
                style={styles.quantityInput}
                value={item.quantity.toString()}
                onChangeText={(text) => updateItem(index, { quantity: Number.parseInt(text) || 1 })}
                keyboardType="numeric"
                placeholder="Qty"
              />
              <Text style={styles.multiplySymbol}>×</Text>
              <TextInput
                style={styles.priceInput}
                value={item.price.toString()}
                onChangeText={(text) => updateItem(index, { price: Number.parseFloat(text) || 0 })}
                keyboardType="numeric"
                placeholder="0.00"
              />
            </View>
            <TouchableOpacity onPress={() => removeItem(index)} style={styles.removeButton}>
              <Ionicons name="trash-outline" size={20} color="#FF3B30" />
            </TouchableOpacity>
          </View>

          <View style={styles.assignmentSection}>
            <Text style={styles.assignmentLabel}>Assign to:</Text>
            {renderMemberChips(index)}
          </View>

          <View style={styles.itemFooter}>
            <Text style={styles.itemTotal}>Total: ${(item.price * item.quantity).toFixed(2)}</Text>
            <Text style={styles.assignedCount}>
              {item.assigned_users?.length || 0} member{(item.assigned_users?.length || 0) !== 1 ? "s" : ""}
            </Text>
          </View>
        </View>
      ))}

      {billItems.length === 0 && (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>No items found</Text>
          <Text style={styles.emptyStateSubtext}>Add items manually or scan a receipt</Text>
        </View>
      )}
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1C1C1E",
  },
  addButton: {
    padding: 8,
  },
  itemContainer: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  itemHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  nameInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: "#D1D1D6",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    marginRight: 8,
  },
  priceQuantityContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  quantityInput: {
    width: 50,
    borderWidth: 1,
    borderColor: "#D1D1D6",
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 8,
    fontSize: 16,
    textAlign: "center",
  },
  multiplySymbol: {
    marginHorizontal: 8,
    fontSize: 16,
    color: "#8E8E93",
  },
  priceInput: {
    width: 80,
    borderWidth: 1,
    borderColor: "#D1D1D6",
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 8,
    fontSize: 16,
    textAlign: "right",
  },
  removeButton: {
    padding: 8,
    marginLeft: 8,
  },
  assignmentSection: {
    marginBottom: 12,
  },
  assignmentLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#8E8E93",
    marginBottom: 8,
  },
  membersList: {
    flexDirection: "row",
    flexWrap: "wrap",
  },
  memberChip: {
    backgroundColor: "#F2F2F7",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  memberChipSelected: {
    backgroundColor: "#007AFF",
  },
  memberChipText: {
    fontSize: 14,
    color: "#1C1C1E",
  },
  memberChipTextSelected: {
    color: "#FFFFFF",
  },
  itemFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: "#F2F2F7",
    paddingTop: 12,
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: "600",
    color: "#007AFF",
  },
  assignedCount: {
    fontSize: 12,
    color: "#8E8E93",
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: "500",
    color: "#8E8E93",
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: "#8E8E93",
    textAlign: "center",
  },
})

export default BillItemParser
