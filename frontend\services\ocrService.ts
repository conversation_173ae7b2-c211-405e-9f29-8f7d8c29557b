export interface OCRResult {
  text: string
  items: Array<{
    name: string
    price: number
    quantity?: number
  }>
  total?: number
  merchant?: string
  date?: string
}

export class OCRService {
  private apiKey: string

  constructor() {
    this.apiKey = process.env.GOOGLE_VISION_API_KEY!
  }

  async processReceipt(imageUri: string): Promise<OCRResult> {
    try {
      // Convert image to base64
      const base64Image = await this.convertImageToBase64(imageUri)

      // Call Google Vision API
      const response = await fetch(`https://vision.googleapis.com/v1/images:annotate?key=${this.apiKey}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          requests: [
            {
              image: {
                content: base64Image,
              },
              features: [
                {
                  type: "TEXT_DETECTION",
                  maxResults: 1,
                },
              ],
            },
          ],
        }),
      })

      const result = await response.json()
      const text = result.responses[0]?.textAnnotations[0]?.description || ""

      return this.parseReceiptText(text)
    } catch (error) {
      console.error("OCR processing error:", error)
      throw new Error("Failed to process receipt")
    }
  }

  private async convertImageToBase64(imageUri: string): Promise<string> {
    const response = await fetch(imageUri)
    const blob = await response.blob()
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64 = (reader.result as string).split(",")[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  private parseReceiptText(text: string): OCRResult {
    const lines = text.split("\n").filter((line) => line.trim())
    const items: Array<{ name: string; price: number; quantity?: number }> = []
    let total = 0
    let merchant = ""
    let date = ""

    // Simple parsing logic - can be enhanced with ML models
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()

      // Try to find merchant (usually first few lines)
      if (i < 3 && !merchant && line.length > 3) {
        merchant = line
      }

      // Try to find date
      const dateMatch = line.match(/\d{1,2}\/\d{1,2}\/\d{2,4}/)
      if (dateMatch && !date) {
        date = dateMatch[0]
      }

      // Try to find items with prices
      const priceMatch = line.match(/(\d+\.?\d*)\s*$/)
      if (priceMatch) {
        const price = Number.parseFloat(priceMatch[1])
        const itemName = line.replace(priceMatch[0], "").trim()

        if (itemName && price > 0) {
          // Check if this might be the total
          if (
            line.toLowerCase().includes("total") ||
            line.toLowerCase().includes("subtotal") ||
            line.toLowerCase().includes("amount")
          ) {
            total = price
          } else {
            items.push({
              name: itemName,
              price: price,
              quantity: 1,
            })
          }
        }
      }
    }

    // If no total found, calculate from items
    if (total === 0 && items.length > 0) {
      total = items.reduce((sum, item) => sum + item.price, 0)
    }

    return {
      text,
      items,
      total,
      merchant,
      date,
    }
  }
}

export const ocrService = new OCRService()
