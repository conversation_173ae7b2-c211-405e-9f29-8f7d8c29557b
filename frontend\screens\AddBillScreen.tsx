"use client"

import React from "react"
import { useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  ActivityIndicator,
} from "react-native"
import { useNavigation, useRoute } from "@react-navigation/native"
import type { StackNavigationProp, RouteProp } from "@react-navigation/stack"
import * as ImagePicker from "expo-image-picker"
import type { RootStackParamList, BillItem, User, ParsedItem } from "../types"
import { useAuth } from "../contexts/AuthProvider"
import { supabase } from "../../backend/supabase/client"
import { ocrService } from "../services/ocrService"
import { uploadImage } from "../../backend/supabase/client"
import InputField from "../components/InputField"
import Button from "../components/Button"
import BillItemParser from "../components/BillItemParser"
import { Ionicons } from "@expo/vector-icons"

type AddBillScreenNavigationProp = StackNavigationProp<RootStackParamList, "AddBill">
type AddBillScreenRouteProp = RouteProp<RootStackParamList, "AddBill">

const AddBillScreen: React.FC = () => {
  const navigation = useNavigation<AddBillScreenNavigationProp>()
  const route = useRoute<AddBillScreenRouteProp>()
  const { user } = useAuth()
  const { groupId } = route.params

  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [tipAmount, setTipAmount] = useState("0")
  const [taxAmount, setTaxAmount] = useState("0")
  const [parsedItems, setParsedItems] = useState<ParsedItem[]>([])
  const [billItems, setBillItems] = useState<BillItem[]>([])
  const [groupMembers, setGroupMembers] = useState<User[]>([])
  const [imageUri, setImageUri] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [ocrLoading, setOcrLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  React.useEffect(() => {
    fetchGroupMembers()
  }, [])

  const fetchGroupMembers = async () => {
    try {
      const { data, error } = await supabase
        .from("group_members")
        .select(`
          user_id,
          users (
            id,
            display_name,
            email
          )
        `)
        .eq("group_id", groupId)

      if (error) throw error

      const members = data?.map((member: any) => member.users).filter(Boolean) || []
      setGroupMembers(members)
    } catch (error) {
      console.error("Error fetching group members:", error)
      Alert.alert("Error", "Failed to load group members")
    }
  }

  const selectImage = async () => {
    Alert.alert("Select Image", "Choose how to add a receipt", [
      { text: "Camera", onPress: () => openCamera() },
      { text: "Gallery", onPress: () => openGallery() },
      { text: "Cancel", style: "cancel" },
    ])
  }

  const openCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync()
    if (status !== "granted") {
      Alert.alert("Permission needed", "Camera permission is required to scan receipts")
      return
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    })

    if (!result.canceled) {
      setImageUri(result.assets[0].uri)
      processImage(result.assets[0].uri)
    }
  }

  const openGallery = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    })

    if (!result.canceled) {
      setImageUri(result.assets[0].uri)
      processImage(result.assets[0].uri)
    }
  }

  const processImage = async (uri: string) => {
    setOcrLoading(true)
    try {
      const result = await ocrService.processImage(uri)
      setParsedItems(result.items)

      if (result.items.length === 0) {
        Alert.alert("No items found", "Unable to detect items in the receipt. You can add items manually.")
      }
    } catch (error) {
      console.error("OCR processing error:", error)
      Alert.alert("Processing Error", "Failed to process the receipt. You can add items manually.")
    } finally {
      setOcrLoading(false)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {}

    if (!title.trim()) {
      newErrors.title = "Bill title is required"
    }

    if (billItems.length === 0) {
      newErrors.items = "At least one item is required"
    }

    const hasUnassignedItems = billItems.some((item) => !item.assigned_users || item.assigned_users.length === 0)
    if (hasUnassignedItems) {
      newErrors.assignment = "All items must be assigned to at least one member"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const saveBill = async () => {
    if (!validateForm()) return
    if (!user) return

    setLoading(true)
    try {
      let imageUrl = null

      if (imageUri) {
        const fileName = `bill_${Date.now()}.jpg`
        const { data: uploadData, error: uploadError } = await uploadImage(imageUri, "bills", fileName)

        if (uploadError) {
          console.error("Image upload error:", uploadError)
        } else if (uploadData) {
          imageUrl = `bills/${fileName}`
        }
      }

      const totalAmount = billItems.reduce((sum, item) => sum + item.price * item.quantity, 0)

      const { data: billData, error: billError } = await supabase
        .from("bills")
        .insert({
          group_id: groupId,
          created_by: user.id,
          title: title.trim(),
          description: description.trim() || null,
          image_url: imageUrl,
          total_amount: totalAmount,
          tip_amount: Number.parseFloat(tipAmount) || 0,
          tax_amount: Number.parseFloat(taxAmount) || 0,
          status: "draft",
        })
        .select()
        .single()

      if (billError) throw billError

      const itemsToInsert = billItems.map((item) => ({
        bill_id: billData.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        assigned_users: item.assigned_users,
      }))

      const { error: itemsError } = await supabase.from("bill_items").insert(itemsToInsert)

      if (itemsError) throw itemsError

      Alert.alert("Success", "Bill created successfully!", [
        {
          text: "OK",
          onPress: () => navigation.navigate("BillSplit", { billId: billData.id }),
        },
      ])
    } catch (error) {
      console.error("Error saving bill:", error)
      Alert.alert("Error", "Failed to save bill. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Add Bill</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <InputField
            label="Bill Title"
            value={title}
            onChangeText={setTitle}
            placeholder="Enter bill title"
            error={errors.title}
          />

          <InputField
            label="Description (Optional)"
            value={description}
            onChangeText={setDescription}
            placeholder="Enter description"
            multiline
            numberOfLines={3}
          />

          <View style={styles.row}>
            <View style={styles.halfField}>
              <InputField
                label="Tip Amount"
                value={tipAmount}
                onChangeText={setTipAmount}
                placeholder="0.00"
                keyboardType="numeric"
              />
            </View>
            <View style={styles.halfField}>
              <InputField
                label="Tax Amount"
                value={taxAmount}
                onChangeText={setTaxAmount}
                placeholder="0.00"
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Receipt Image</Text>
            <TouchableOpacity onPress={selectImage} style={styles.addImageButton}>
              <Ionicons name="camera-outline" size={20} color="#007AFF" />
              <Text style={styles.addImageText}>Add Image</Text>
            </TouchableOpacity>
          </View>

          {imageUri && (
            <View style={styles.imageContainer}>
              <Text style={styles.imageText}>Receipt image added</Text>
              {ocrLoading && (
                <View style={styles.ocrLoading}>
                  <ActivityIndicator size="small" color="#007AFF" />
                  <Text style={styles.ocrLoadingText}>Processing image...</Text>
                </View>
              )}
            </View>
          )}
        </View>

        <View style={styles.section}>
          <BillItemParser items={parsedItems} groupMembers={groupMembers} onItemsChange={setBillItems} />
          {errors.items && <Text style={styles.errorText}>{errors.items}</Text>}
          {errors.assignment && <Text style={styles.errorText}>{errors.assignment}</Text>}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title="Save & Continue"
          onPress={saveBill}
          loading={loading}
          disabled={loading || ocrLoading}
          variant="primary"
        />
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F2F2F7",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5EA",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1C1C1E",
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: "#FFFFFF",
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1C1C1E",
  },
  addImageButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  addImageText: {
    fontSize: 16,
    color: "#007AFF",
    marginLeft: 4,
  },
  imageContainer: {
    padding: 16,
    backgroundColor: "#F2F2F7",
    borderRadius: 8,
  },
  imageText: {
    fontSize: 14,
    color: "#1C1C1E",
    textAlign: "center",
  },
  ocrLoading: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 8,
  },
  ocrLoadingText: {
    fontSize: 14,
    color: "#007AFF",
    marginLeft: 8,
  },
  row: {
    flexDirection: "row",
    marginHorizontal: -8,
  },
  halfField: {
    flex: 1,
    marginHorizontal: 8,
  },
  errorText: {
    fontSize: 14,
    color: "#FF3B30",
    marginTop: 8,
  },
  footer: {
    padding: 20,
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E5E5EA",
  },
})

export default AddBillScreen
