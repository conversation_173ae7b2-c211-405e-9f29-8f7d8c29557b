export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  created_at: string
}

export interface Group {
  id: string
  name: string
  description?: string
  created_by: string
  created_at: string
  members: GroupMember[]
  total_expenses: number
}

export interface GroupMember {
  id: string
  group_id: string
  user_id: string
  role: "admin" | "member"
  joined_at: string
  user: User
}

export interface Bill {
  id: string
  group_id: string
  created_by: string
  title: string
  description?: string
  total_amount: number
  currency: string
  receipt_image?: string
  created_at: string
  items: BillItem[]
  splits: BillSplit[]
  status: "pending" | "settled" | "cancelled"
}

export interface BillItem {
  id: string
  bill_id: string
  name: string
  price: number
  quantity: number
  category?: string
}

export interface BillSplit {
  id: string
  bill_id: string
  user_id: string
  amount: number
  paid: boolean
  paid_at?: string
  user: User
}

export interface Payment {
  id: string
  from_user_id: string
  to_user_id: string
  amount: number
  currency: string
  bill_id?: string
  group_id: string
  status: "pending" | "completed" | "failed"
  payment_method: "stripe" | "paypal" | "venmo" | "cash"
  created_at: string
  completed_at?: string
}

export interface Notification {
  id: string
  user_id: string
  title: string
  message: string
  type: "bill_added" | "payment_request" | "payment_received" | "group_invite"
  read: boolean
  data?: any
  created_at: string
}

export interface ChatMessage {
  id: string
  group_id: string
  user_id: string
  message: string
  created_at: string
  user: User
}

export type RootStackParamList = {
  Splash: undefined
  Onboarding: undefined
  Login: undefined
  Signup: undefined
  ForgotPassword: undefined
  Dashboard: undefined
  CreateGroup: undefined
  JoinGroup: undefined
  GroupList: undefined
  GroupDetails: { groupId: string }
  AddBill: { groupId: string }
  BillEdit: { billId: string }
  BillSplit: { billId: string }
  BillDetails: { billId: string }
  PaymentHistory: undefined
  PaymentRequest: { userId: string; amount: number }
  ExpenseTracking: undefined
  Profile: undefined
  Settings: undefined
  Notifications: undefined
  GroupChat: { groupId: string }
  GroupMembers: { groupId: string }
  GroupSettings: { groupId: string }
}
