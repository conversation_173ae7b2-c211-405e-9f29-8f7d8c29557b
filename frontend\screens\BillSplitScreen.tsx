"use client"

import type React from "react"
import { useState, useCallback } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, SafeAreaView } from "react-native"
import { useNavigation, useRoute, useFocusEffect } from "@react-navigation/native"
import type { StackNavigationProp, RouteProp } from "@react-navigation/stack"
import type { RootStackParamList, Bill, User } from "../types"
import { useAuth } from "../contexts/AuthProvider"
import { supabase } from "../../backend/supabase/client"
import { paymentService, type PaymentCalculation } from "../services/paymentService"
import Button from "../components/Button"
import InputField from "../components/InputField"
import { Ionicons } from "@expo/vector-icons"

type BillSplitScreenNavigationProp = StackNavigationProp<RootStackParamList, "BillSplit">
type BillSplitScreenRouteProp = RouteProp<RootStackParamList, "BillSplit">

const BillSplitScreen: React.FC = () => {
  const navigation = useNavigation<BillSplitScreenNavigationProp>()
  const route = useRoute<BillSplitScreenRouteProp>()
  const { user } = useAuth()
  const { billId } = route.params

  const [bill, setBill] = useState<Bill | null>(null)
  const [groupMembers, setGroupMembers] = useState<User[]>([])
  const [splitMode, setSplitMode] = useState<"equal" | "by_item" | "custom">("equal")
  const [calculations, setCalculations] = useState<PaymentCalculation[]>([])
  const [customAmounts, setCustomAmounts] = useState<{ [userId: string]: string }>({})
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const fetchBillData = async () => {
    try {
      // Fetch bill details
      const { data: billData, error: billError } = await supabase
        .from("bills")
        .select(`
          *,
          creator:users!bills_created_by_fkey(display_name),
          group:groups(name)
        `)
        .eq("id", billId)
        .single()

      if (billError) throw billError
      setBill(billData)

      // Fetch group members
      const { data: membersData, error: membersError } = await supabase
        .from("group_members")
        .select(`
          user_id,
          users (
            id,
            display_name,
            email
          )
        `)
        .eq("group_id", billData.group_id)

      if (membersError) throw membersError

      const members = membersData?.map((member: any) => member.users).filter(Boolean) || []
      setGroupMembers(members)

      // Initialize custom amounts
      const initialAmounts: { [userId: string]: string } = {}
      members.forEach((member) => {
        initialAmounts[member.id] = "0.00"
      })
      setCustomAmounts(initialAmounts)

      // Calculate equal split by default
      await calculateSplit("equal", billData, members)
    } catch (error) {
      console.error("Error fetching bill data:", error)
      Alert.alert("Error", "Failed to load bill details")
    } finally {
      setLoading(false)
    }
  }

  useFocusEffect(
    useCallback(() => {
      fetchBillData()
    }, [billId]),
  )

  const calculateSplit = async (mode: "equal" | "by_item" | "custom", billData?: Bill, members?: User[]) => {
    try {
      const currentBill = billData || bill
      const currentMembers = members || groupMembers

      if (!currentBill || currentMembers.length === 0) return

      let result
      switch (mode) {
        case "equal":
          result = await paymentService.calculateEqualSplit(
            currentBill,
            currentMembers.map((m) => m.id),
          )
          break
        case "by_item":
          result = await paymentService.calculateItemBasedSplit(billId)
          break
        case "custom":
          const amounts: { [userId: string]: number } = {}
          Object.entries(customAmounts).forEach(([userId, amount]) => {
            amounts[userId] = Number.parseFloat(amount) || 0
          })
          result = await paymentService.calculateCustomSplit(currentBill, amounts)
          break
      }

      setCalculations(result.calculations)
    } catch (error) {
      console.error("Error calculating split:", error)
      Alert.alert("Error", "Failed to calculate split")
    }
  }

  const handleSplitModeChange = (mode: "equal" | "by_item" | "custom") => {
    setSplitMode(mode)
    calculateSplit(mode)
  }

  const handleCustomAmountChange = (userId: string, amount: string) => {
    setCustomAmounts((prev) => ({
      ...prev,
      [userId]: amount,
    }))
  }

  const validateCustomAmounts = () => {
    if (splitMode !== "custom") return true

    const total = Object.values(customAmounts).reduce((sum, amount) => sum + (Number.parseFloat(amount) || 0), 0)
    const billTotal = bill ? bill.total_amount + bill.tip_amount + bill.tax_amount : 0

    if (Math.abs(total - billTotal) > 0.01) {
      Alert.alert(
        "Invalid Split",
        `Custom amounts must total $${billTotal.toFixed(2)}. Current total: $${total.toFixed(2)}`,
      )
      return false
    }

    return true
  }

  const saveSplit = async () => {
    if (!validateCustomAmounts()) return

    setSaving(true)
    try {
      await paymentService.savePayments(billId, calculations)

      Alert.alert("Success", "Bill split saved successfully!", [
        {
          text: "OK",
          onPress: () => navigation.goBack(),
        },
      ])
    } catch (error) {
      console.error("Error saving split:", error)
      Alert.alert("Error", "Failed to save split. Please try again.")
    } finally {
      setSaving(false)
    }
  }

  const getMemberName = (userId: string) => {
    const member = groupMembers.find((m) => m.id === userId)
    return member?.display_name || "Unknown"
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </SafeAreaView>
    )
  }

  if (!bill) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Bill not found</Text>
          <Button title="Go Back" onPress={() => navigation.goBack()} variant="primary" />
        </View>
      </SafeAreaView>
    )
  }

  const totalAmount = bill.total_amount + bill.tip_amount + bill.tax_amount

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Split Bill</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        {/* Bill Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Bill Summary</Text>
          <View style={styles.billSummary}>
            <Text style={styles.billTitle}>{bill.title}</Text>
            <Text style={styles.billTotal}>${totalAmount.toFixed(2)}</Text>
          </View>
          <Text style={styles.billMeta}>
            {bill.group?.name} • {groupMembers.length} member{groupMembers.length !== 1 ? "s" : ""}
          </Text>
        </View>

        {/* Split Mode Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Split Method</Text>
          <View style={styles.splitModeContainer}>
            <TouchableOpacity
              style={[styles.splitModeButton, splitMode === "equal" && styles.splitModeButtonActive]}
              onPress={() => handleSplitModeChange("equal")}
            >
              <Ionicons name="people-outline" size={20} color={splitMode === "equal" ? "#FFFFFF" : "#007AFF"} />
              <Text style={[styles.splitModeText, splitMode === "equal" && styles.splitModeTextActive]}>
                Equal Split
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.splitModeButton, splitMode === "by_item" && styles.splitModeButtonActive]}
              onPress={() => handleSplitModeChange("by_item")}
            >
              <Ionicons name="list-outline" size={20} color={splitMode === "by_item" ? "#FFFFFF" : "#007AFF"} />
              <Text style={[styles.splitModeText, splitMode === "by_item" && styles.splitModeTextActive]}>
                By Items
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.splitModeButton, splitMode === "custom" && styles.splitModeButtonActive]}
              onPress={() => handleSplitModeChange("custom")}
            >
              <Ionicons name="calculator-outline" size={20} color={splitMode === "custom" ? "#FFFFFF" : "#007AFF"} />
              <Text style={[styles.splitModeText, splitMode === "custom" && styles.splitModeTextActive]}>Custom</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Split Results */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Split Results</Text>

          {splitMode === "custom" ? (
            <View style={styles.customSplitContainer}>
              {groupMembers.map((member) => (
                <View key={member.id} style={styles.customSplitRow}>
                  <Text style={styles.memberName}>{member.display_name}</Text>
                  <View style={styles.customAmountInput}>
                    <Text style={styles.currencySymbol}>$</Text>
                    <InputField
                      label=""
                      value={customAmounts[member.id] || "0.00"}
                      onChangeText={(value) => handleCustomAmountChange(member.id, value)}
                      keyboardType="numeric"
                      placeholder="0.00"
                    />
                  </View>
                </View>
              ))}

              <View style={styles.customSplitTotal}>
                <Text style={styles.totalLabel}>Total:</Text>
                <Text style={styles.totalValue}>
                  $
                  {Object.values(customAmounts)
                    .reduce((sum, amount) => sum + (Number.parseFloat(amount) || 0), 0)
                    .toFixed(2)}
                </Text>
              </View>
            </View>
          ) : (
            <View style={styles.splitResults}>
              {calculations.map((calc) => (
                <View key={calc.userId} style={styles.splitResultRow}>
                  <View style={styles.memberInfo}>
                    <Text style={styles.memberName}>{getMemberName(calc.userId)}</Text>
                    {splitMode === "by_item" && <Text style={styles.memberItems}>{calc.items.join(", ")}</Text>}
                  </View>
                  <Text style={styles.memberAmount}>${calc.amount.toFixed(2)}</Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <Button
            title="Save Split"
            onPress={saveSplit}
            loading={saving}
            disabled={saving}
            variant="primary"
            style={styles.actionButton}
          />

          <Button
            title="Recalculate"
            onPress={() => calculateSplit(splitMode)}
            variant="secondary"
            style={styles.actionButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F2F2F7",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5EA",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1C1C1E",
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  errorText: {
    fontSize: 18,
    color: "#FF3B30",
    marginBottom: 20,
  },
  section: {
    backgroundColor: "#FFFFFF",
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1C1C1E",
    marginBottom: 16,
  },
  billSummary: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  billTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1C1C1E",
  },
  billTotal: {
    fontSize: 24,
    fontWeight: "700",
    color: "#007AFF",
  },
  billMeta: {
    fontSize: 14,
    color: "#8E8E93",
  },
  splitModeContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  splitModeButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#007AFF",
  },
  splitModeButtonActive: {
    backgroundColor: "#007AFF",
  },
  splitModeText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#007AFF",
    marginLeft: 4,
  },
  splitModeTextActive: {
    color: "#FFFFFF",
  },
  splitResults: {},
  splitResultRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F2F2F7",
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1C1C1E",
    marginBottom: 2,
  },
  memberItems: {
    fontSize: 12,
    color: "#8E8E93",
  },
  memberAmount: {
    fontSize: 18,
    fontWeight: "600",
    color: "#007AFF",
  },
  customSplitContainer: {},
  customSplitRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  customAmountInput: {
    flexDirection: "row",
    alignItems: "center",
    width: 120,
  },
  currencySymbol: {
    fontSize: 16,
    color: "#1C1C1E",
    marginRight: 4,
  },
  customSplitTotal: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: "#F2F2F7",
    paddingTop: 16,
    marginTop: 16,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1C1C1E",
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "700",
    color: "#007AFF",
  },
  actionButton: {
    marginBottom: 12,
  },
})

export default BillSplitScreen
