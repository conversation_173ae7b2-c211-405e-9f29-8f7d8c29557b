"use client"

import type React from "react"
import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, Alert } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { Button } from "../components/Button"
import { InputField } from "../components/InputField"
import { useAuth } from "../contexts/AuthProvider"
import { supabase } from "../services/supabase"
import type { StackNavigationProp } from "@react-navigation/stack"
import type { RootStackParamList } from "../types"

type CreateGroupScreenNavigationProp = StackNavigationProp<RootStackParamList, "CreateGroup">

interface Props {
  navigation: CreateGroupScreenNavigationProp
}

const CreateGroupScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuth()
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [loading, setLoading] = useState(false)

  const handleCreateGroup = async () => {
    if (!name.trim()) {
      Alert.alert("Error", "Please enter a group name")
      return
    }

    if (!user) return

    setLoading(true)
    try {
      // Create group
      const { data: groupData, error: groupError } = await supabase
        .from("groups")
        .insert([
          {
            name: name.trim(),
            description: description.trim() || null,
            created_by: user.id,
          },
        ])
        .select()
        .single()

      if (groupError) throw groupError

      // Add creator as admin member
      const { error: memberError } = await supabase.from("group_members").insert([
        {
          group_id: groupData.id,
          user_id: user.id,
          role: "admin",
        },
      ])

      if (memberError) throw memberError

      Alert.alert("Success", "Group created successfully!", [
        {
          text: "OK",
          onPress: () => navigation.navigate("GroupDetails", { groupId: groupData.id }),
        },
      ])
    } catch (error) {
      console.error("Error creating group:", error)
      Alert.alert("Error", "Failed to create group. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Button title="Cancel" onPress={() => navigation.goBack()} variant="outline" size="small" />
        <Text style={styles.title}>Create Group</Text>
        <View style={{ width: 60 }} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.form}>
          <InputField label="Group Name" placeholder="Enter group name" value={name} onChangeText={setName} />
          <InputField
            label="Description (Optional)"
            placeholder="What's this group for?"
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={3}
          />

          <View style={styles.info}>
            <Text style={styles.infoTitle}>💡 Tips for creating groups:</Text>
            <Text style={styles.infoText}>• Use descriptive names like "Roommates" or "Europe Trip 2024"</Text>
            <Text style={styles.infoText}>• Add a description to help members understand the group's purpose</Text>
            <Text style={styles.infoText}>• You can invite members after creating the group</Text>
          </View>

          <Button title="Create Group" onPress={handleCreateGroup} loading={loading} size="large" />
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  form: {
    paddingTop: 24,
    gap: 20,
  },
  info: {
    backgroundColor: "#f8f9fa",
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
    lineHeight: 20,
  },
})

export default CreateGroupScreen
