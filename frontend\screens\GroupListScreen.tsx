"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { View, Text, StyleSheet, ScrollView, RefreshControl, TouchableOpacity } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { Button } from "../components/Button"
import { GroupCard } from "../components/GroupCard"
import { useAuth } from "../contexts/AuthProvider"
import { supabase } from "../services/supabase"
import type { StackNavigationProp } from "@react-navigation/stack"
import type { RootStackParamList, Group } from "../types"

type GroupListScreenNavigationProp = StackNavigationProp<RootStackParamList, "GroupList">

interface Props {
  navigation: GroupListScreenNavigationProp
}

const GroupListScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuth()
  const [groups, setGroups] = useState<Group[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchGroups()
  }, [])

  const fetchGroups = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from("group_members")
        .select(`
          groups (
            id,
            name,
            description,
            created_by,
            created_at,
            total_expenses,
            group_members (
              id,
              user_id,
              role,
              users (
                id,
                name,
                email
              )
            )
          )
        `)
        .eq("user_id", user.id)

      if (error) throw error

      const groupsData =
        data
          ?.map((item) => ({
            ...item.groups,
            members: item.groups.group_members.map((member) => ({
              ...member,
              user: member.users,
            })),
          }))
          .filter(Boolean) || []

      setGroups(groupsData)
    } catch (error) {
      console.error("Error fetching groups:", error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const onRefresh = () => {
    setRefreshing(true)
    fetchGroups()
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>My Groups</Text>
        <View style={{ width: 60 }} />
      </View>

      <View style={styles.actions}>
        <Button title="Create Group" onPress={() => navigation.navigate("CreateGroup")} size="medium" />
        <Button title="Join Group" onPress={() => navigation.navigate("JoinGroup")} variant="outline" size="medium" />
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {groups.length > 0 ? (
          groups.map((group) => (
            <GroupCard
              key={group.id}
              group={group}
              onPress={() => navigation.navigate("GroupDetails", { groupId: group.id })}
            />
          ))
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyEmoji}>👥</Text>
            <Text style={styles.emptyTitle}>No Groups Yet</Text>
            <Text style={styles.emptyText}>
              Create your first group or join an existing one to start splitting bills with friends!
            </Text>
            <View style={styles.emptyActions}>
              <Button title="Create Your First Group" onPress={() => navigation.navigate("CreateGroup")} size="large" />
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  backButton: {
    fontSize: 16,
    color: "#007AFF",
    fontWeight: "600",
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  actions: {
    flexDirection: "row",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#fff",
    gap: 12,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 60,
  },
  emptyEmoji: {
    fontSize: 80,
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  emptyActions: {
    width: "100%",
    paddingHorizontal: 40,
  },
})

export default GroupListScreen
