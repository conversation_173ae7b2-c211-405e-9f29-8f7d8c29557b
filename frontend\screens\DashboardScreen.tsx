"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { View, Text, StyleSheet, ScrollView, RefreshControl, TouchableOpacity, Alert, Platform, Modal, Dimensions } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { Button } from "../components/Button"
import { GroupCard } from "../components/GroupCard"
import { BillCard } from "../components/BillCard"
import { useAuth } from "../contexts/AuthProvider"
import { supabase } from "../services/supabase"
import { formatCurrency } from "../utils/formatters"
import type { StackNavigationProp } from "@react-navigation/stack"
import type { RootStackParamList, Group, Bill } from "../types"
import AsyncStorage from '@react-native-async-storage/async-storage'

type DashboardScreenNavigationProp = StackNavigationProp<RootStackParamList, "Dashboard">

interface Props {
  navigation: DashboardScreenNavigationProp
}

const DashboardScreen: React.FC<Props> = ({ navigation }) => {
  const { user, signOut } = useAuth()
  const [groups, setGroups] = useState<Group[]>([])
  const [recentBills, setRecentBills] = useState<Bill[]>([])
  const [totalOwed, setTotalOwed] = useState(0)
  const [totalOwing, setTotalOwing] = useState(0)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [showLogoutModal, setShowLogoutModal] = useState(false)
  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false)
  const [welcomeCountdown, setWelcomeCountdown] = useState(4)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  // Debug user data
  useEffect(() => {
    if (user) {
      console.log('Dashboard - User data:', user)
      console.log('Dashboard - User name:', user.name)
      console.log('Dashboard - User email:', user.email)
    }
  }, [user])

  // Check if user just registered and show welcome message
  useEffect(() => {
    const checkRegistrationStatus = async () => {
      try {
        const justRegistered = await AsyncStorage.getItem('justRegistered')
        if (justRegistered === 'true') {
          setShowWelcomeMessage(true)
          setWelcomeCountdown(4)
          // Clear the flag
          await AsyncStorage.removeItem('justRegistered')
        }
      } catch (error) {
        console.error('Error checking registration status:', error)
      }
    }

    checkRegistrationStatus()
  }, [])

  // Handle welcome message countdown
  useEffect(() => {
    let interval: ReturnType<typeof setInterval>
    if (showWelcomeMessage && welcomeCountdown > 0) {
      interval = setInterval(() => {
        setWelcomeCountdown((prev) => prev - 1)
      }, 1000)
    } else if (showWelcomeMessage && welcomeCountdown === 0) {
      setShowWelcomeMessage(false)
    }

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [showWelcomeMessage, welcomeCountdown])

  const fetchDashboardData = async () => {
    try {
      await Promise.all([fetchGroups(), fetchRecentBills(), fetchBalances()])
    } catch (error) {
      console.error("Error fetching dashboard data:", error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  const fetchGroups = async () => {
    if (!user) return

    const { data, error } = await supabase
      .from("group_members")
      .select(`
        groups (
          id,
          name,
          description,
          created_by,
          created_at,
          total_expenses
        )
      `)
      .eq("user_id", user.id)
      .limit(5)

    if (error) throw error
    setGroups(data?.map((item) => item.groups).filter(Boolean) || [])
  }

  const fetchRecentBills = async () => {
    if (!user) return

    const { data, error } = await supabase
      .from("bills")
      .select(`
        *,
        items:bill_items(*),
        splits:bill_splits(*)
      `)
      .order("created_at", { ascending: false })
      .limit(5)

    if (error) throw error
    setRecentBills(data || [])
  }

  const fetchBalances = async () => {
    if (!user) return

    // Calculate total owed to user
    const { data: owedData, error: owedError } = await supabase
      .from("bill_splits")
      .select("amount")
      .eq("paid", false)
      .neq("user_id", user.id)

    if (owedError) throw owedError
    const owed = owedData?.reduce((sum, split) => sum + split.amount, 0) || 0
    setTotalOwed(owed)

    // Calculate total user owes
    const { data: owingData, error: owingError } = await supabase
      .from("bill_splits")
      .select("amount")
      .eq("user_id", user.id)
      .eq("paid", false)

    if (owingError) throw owingError
    const owing = owingData?.reduce((sum, split) => sum + split.amount, 0) || 0
    setTotalOwing(owing)
  }

  const onRefresh = () => {
    setRefreshing(true)
    fetchDashboardData()
  }

  const handleLogout = async () => {
    console.log("handleLogout called")
    setShowLogoutModal(true)
  }

  const confirmLogout = async () => {
    setShowLogoutModal(false)
    try {
      await signOut()
      navigation.navigate('Login')
    } catch (error) {
      console.error("Logout error:", error)
      // Show error modal or alert
    }
  }

  const cancelLogout = () => {
    setShowLogoutModal(false)
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <View style={styles.header}>
          <View style={styles.headerTextContainer}>
            <Text style={styles.greeting}>Hello, {user?.name}!</Text>
            <Text style={styles.subtitle}>Here's your spending overview</Text>
          </View>
          <View style={styles.headerButtons}>
            <TouchableOpacity style={styles.profileButton} onPress={() => navigation.navigate("Profile")}>
              <Text style={styles.profileIcon}>👤</Text>
            </TouchableOpacity>
            <View style={styles.buttonSpacer} />
            <TouchableOpacity
              style={styles.logoutButton}
              onPress={() => {
                console.log("Logout button pressed")
                handleLogout()
              }}
            >
              <Text style={styles.logoutText}>Logout</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.balanceCards}>
          <View style={[styles.balanceCard, styles.owedCard]}>
            <Text style={styles.balanceLabel}>You're owed</Text>
            <Text style={styles.balanceAmount}>{formatCurrency(totalOwed)}</Text>
          </View>
          <View style={[styles.balanceCard, styles.owingCard]}>
            <Text style={styles.balanceLabel}>You owe</Text>
            <Text style={styles.balanceAmount}>{formatCurrency(totalOwing)}</Text>
          </View>
        </View>

        <View style={styles.quickActions}>
          <Button title="Add Bill" onPress={() => navigation.navigate("AddBill", { groupId: "" })} size="medium" />
          <Button
            title="Create Group"
            onPress={() => navigation.navigate("CreateGroup")}
            variant="outline"
            size="medium"
          />
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Groups</Text>
            <TouchableOpacity onPress={() => navigation.navigate("GroupList")}>
              <Text style={styles.seeAll}>See All</Text>
            </TouchableOpacity>
          </View>
          {groups.length > 0 ? (
            groups.map((group) => (
              <GroupCard
                key={group.id}
                group={group}
                onPress={() => navigation.navigate("GroupDetails", { groupId: group.id })}
              />
            ))
          ) : (
            <Text style={styles.emptyText}>No groups yet. Create your first group!</Text>
          )}
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Bills</Text>
            <TouchableOpacity onPress={() => navigation.navigate("PaymentHistory")}>
              <Text style={styles.seeAll}>See All</Text>
            </TouchableOpacity>
          </View>
          {recentBills.length > 0 ? (
            recentBills.map((bill) => (
              <BillCard
                key={bill.id}
                bill={bill}
                onPress={() => navigation.navigate("BillDetails", { billId: bill.id })}
              />
            ))
          ) : (
            <Text style={styles.emptyText}>No bills yet. Add your first bill!</Text>
          )}
        </View>
      </ScrollView>

      {/* Welcome Message Modal */}
      <Modal
        visible={showWelcomeMessage}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowWelcomeMessage(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.welcomeModalContainer}>
            <Text style={styles.welcomeIcon}>🎉</Text>
            <Text style={styles.welcomeTitle}>Registration Successful!</Text>
            <Text style={styles.welcomeMessage}>
              Welcome to SmartSplit, {user?.name}! Your account has been created successfully.
            </Text>
            <Text style={styles.welcomeCountdownText}>
              This message will close in {welcomeCountdown} seconds
            </Text>
            <TouchableOpacity
              style={styles.welcomeCloseButton}
              onPress={() => setShowWelcomeMessage(false)}
            >
              <Text style={styles.welcomeCloseButtonText}>Continue</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Custom Logout Modal */}
      <Modal
        visible={showLogoutModal}
        transparent={true}
        animationType="fade"
        onRequestClose={cancelLogout}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Logout</Text>
            <Text style={styles.modalMessage}>Are you sure you want to logout?</Text>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity style={styles.cancelButton} onPress={cancelLogout}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.logoutConfirmButton} onPress={confirmLogout}>
                <Text style={styles.logoutConfirmButtonText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Platform.OS === 'web' ? 20 : 16,
    paddingVertical: Platform.OS === 'web' ? 16 : 12,
    minHeight: 60,
  },
  headerTextContainer: {
    flex: 1,
    marginRight: 16,
  },
  greeting: {
    fontSize: Platform.OS === 'web' ? 24 : 20,
    fontWeight: "bold",
    color: "#333",
    flexShrink: 1,
  },
  subtitle: {
    fontSize: Platform.OS === 'web' ? 16 : 14,
    color: "#666",
    marginTop: 4,
    flexShrink: 1,
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#007AFF",
    alignItems: "center",
    justifyContent: "center",
  },
  profileIcon: {
    fontSize: 20,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonSpacer: {
    width: 12,
  },
  logoutButton: {
    backgroundColor: '#FF3B30',
    paddingHorizontal: Platform.OS === 'web' ? 12 : 10,
    paddingVertical: Platform.OS === 'web' ? 8 : 6,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: Platform.OS === 'web' ? 'auto' : 60,
  },
  logoutText: {
    color: '#fff',
    fontSize: Platform.OS === 'web' ? 14 : 12,
    fontWeight: '600',
  },
  balanceCards: {
    flexDirection: "row",
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  balanceCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
  },
  owedCard: {
    backgroundColor: "#E8F5E8",
  },
  owingCard: {
    backgroundColor: "#FFF2E8",
  },
  balanceLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  balanceAmount: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  quickActions: {
    flexDirection: "row",
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  seeAll: {
    fontSize: 16,
    color: "#007AFF",
    fontWeight: "600",
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    fontStyle: "italic",
    paddingVertical: 20,
  },
  logoutSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  logoutButtonMain: {
    backgroundColor: "#FF3B30",
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
  },
  logoutButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "bold",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    margin: 20,
    minWidth: 280,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
    textAlign: 'center',
    lineHeight: 22,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: '600',
  },
  logoutConfirmButton: {
    flex: 1,
    backgroundColor: '#FF3B30',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  logoutConfirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  welcomeModalContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 32,
    margin: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  welcomeIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  welcomeMessage: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 24,
  },
  welcomeCountdownText: {
    fontSize: 14,
    color: '#007AFF',
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: '500',
  },
  welcomeCloseButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  welcomeCloseButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
})





















export default DashboardScreen
