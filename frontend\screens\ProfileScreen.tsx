import React, { useState, useEffect } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
  Platform,
} from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { LinearGradient } from "expo-linear-gradient"
import { Ionicons } from "@expo/vector-icons"
import * as ImagePicker from "expo-image-picker"
import { Button } from "../components/Button"
import { InputField } from "../components/InputField"
import { useAuth } from "../contexts/AuthProvider"
import { supabase } from "../services/supabase"
import type { StackNavigationProp } from "@react-navigation/stack"
import type { RootStackParamList, User } from "../types"

type ProfileScreenNavigationProp = StackNavigationProp<RootStackParamList, "Profile">

interface Props {
  navigation: ProfileScreenNavigationProp
}

export default function ProfileScreen({ navigation }: Props) {
  const { user, signOut } = useAuth()
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [displayName, setDisplayName] = useState(user?.name || "")
  const [currency, setCurrency] = useState("USD")
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar || "")
  const [nameError, setNameError] = useState("")

  useEffect(() => {
    if (user) {
      setDisplayName(user.name)
      setAvatarUrl(user.avatar || "")
      // You can add currency fetching from user preferences here
    }
  }, [user])

  const validateName = (name: string): boolean => {
    if (!name.trim()) {
      setNameError("Display name is required")
      return false
    }
    if (name.trim().length < 2) {
      setNameError("Display name must be at least 2 characters")
      return false
    }
    setNameError("")
    return true
  }

  const handleUpdateProfile = async () => {
    if (!validateName(displayName)) return

    setLoading(true)
    try {
      console.log("Updating profile for user ID:", user?.id)
      console.log("New name:", displayName.trim())
      console.log("Avatar URL:", avatarUrl)

      // First, let's check if the user exists in the table
      const { data: existingUser, error: fetchError } = await supabase
        .from("users")
        .select("*")
        .eq("id", user?.id)
        .single()

      if (fetchError) {
        console.error("Error fetching user:", fetchError)
        if (fetchError.code === 'PGRST116') {
          // User doesn't exist, let's create them
          console.log("User doesn't exist, creating new user record...")
          const { data: newUser, error: insertError } = await supabase
            .from("users")
            .insert({
              id: user?.id,
              email: user?.email,
              name: displayName.trim(),
              avatar: avatarUrl,
            })
            .select()

          if (insertError) {
            console.error("Error creating user:", insertError)
            throw insertError
          }
          console.log("User created successfully:", newUser)
          Alert.alert("Success", "Profile created and updated successfully!")
          return
        } else {
          throw fetchError
        }
      }

      console.log("Existing user found:", existingUser)

      // Update the existing user
      const { data, error } = await supabase
        .from("users")
        .update({
          name: displayName.trim(),
          avatar: avatarUrl,
        })
        .eq("id", user?.id)
        .select()

      if (error) {
        console.error("Supabase update error:", error)
        throw error
      }

      console.log("Profile updated successfully:", data)
      Alert.alert("Success", "Profile updated successfully!")
    } catch (error) {
      console.error("Error updating profile:", error)
      Alert.alert("Error", `Failed to update profile: ${error.message || 'Please try again.'}`)
    } finally {
      setLoading(false)
    }
  }

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
      if (status !== "granted") {
        Alert.alert("Permission needed", "Please grant camera roll permissions to upload an avatar.")
        return
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      })

      if (!result.canceled && result.assets[0]) {
        await uploadAvatar(result.assets[0].uri)
      }
    } catch (error) {
      console.error("Error picking image:", error)
      Alert.alert("Error", "Failed to pick image. Please try again.")
    }
  }

  const uploadAvatar = async (uri: string) => {
    setUploading(true)
    try {
      const fileExt = uri.split(".").pop()
      const fileName = `${user?.id}-${Date.now()}.${fileExt}`
      const filePath = fileName // Just the filename, not nested in avatars folder

      // Convert URI to blob for upload
      const response = await fetch(uri)
      const blob = await response.blob()

      console.log("Uploading to avatars bucket:", filePath)

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(filePath, blob, {
          cacheControl: '3600',
          upsert: true // Allow overwriting existing files
        })

      if (uploadError) {
        console.error("Upload error:", uploadError)
        throw uploadError
      }

      console.log("Upload successful:", uploadData)

      // Get public URL
      const { data } = supabase.storage.from("avatars").getPublicUrl(filePath)

      console.log("Public URL:", data.publicUrl)
      setAvatarUrl(data.publicUrl)

      Alert.alert("Success", "Avatar uploaded successfully!")
    } catch (error) {
      console.error("Error uploading avatar:", error)
      Alert.alert("Error", "Failed to upload avatar. Please try again.")
    } finally {
      setUploading(false)
    }
  }

  const handleLogout = () => {
    Alert.alert(
      "Logout",
      "Are you sure you want to logout?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Logout",
          style: "destructive",
          onPress: async () => {
            try {
              await signOut()
              navigation.navigate("Login")
            } catch (error) {
              console.error("Error signing out:", error)
              Alert.alert("Error", "Failed to logout. Please try again.")
            }
          },
        },
      ]
    )
  }

  return (
    <LinearGradient colors={["#667eea", "#764ba2"]} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile</Text>
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.profileContainer}>
            {/* Avatar Section */}
            <View style={styles.avatarSection}>
              <TouchableOpacity style={styles.avatarContainer} onPress={pickImage} disabled={uploading}>
                {avatarUrl ? (
                  <Image source={{ uri: avatarUrl }} style={styles.avatar} />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <Ionicons name="person" size={40} color="#666" />
                  </View>
                )}
                <View style={styles.avatarEditIcon}>
                  <Ionicons name="camera" size={16} color="#fff" />
                </View>
              </TouchableOpacity>
              <Text style={styles.avatarText}>
                {uploading ? "Uploading..." : "Tap to change avatar"}
              </Text>
            </View>

            {/* Profile Form */}
            <View style={styles.formContainer}>
              <View style={styles.form}>
                <InputField
                  label="Display Name"
                  placeholder="Enter your display name"
                  value={displayName}
                  onChangeText={(text) => {
                    setDisplayName(text)
                    if (nameError) setNameError("")
                  }}
                  error={nameError}
                />

                <View style={styles.emailContainer}>
                  <Text style={styles.emailLabel}>Email</Text>
                  <View style={styles.emailField}>
                    <Text style={styles.emailText}>{user?.email || ""}</Text>
                    <Text style={styles.emailNote}>Email cannot be changed</Text>
                  </View>
                </View>

                <View style={styles.currencySection}>
                  <Text style={styles.currencyLabel}>Preferred Currency</Text>
                  <View style={styles.currencyContainer}>
                    <Text style={styles.currencyValue}>USD ($)</Text>
                    <Text style={styles.currencyNote}>Currency settings coming soon</Text>
                  </View>
                </View>

                <Button
                  title="Update Profile"
                  onPress={handleUpdateProfile}
                  loading={loading}
                  size="large"
                  style={styles.updateButton}
                />
              </View>
            </View>

            {/* Logout Section */}
            <View style={styles.logoutSection}>
              <Button
                title="Logout"
                onPress={handleLogout}
                variant="outline"
                size="large"
                style={styles.logoutButton}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  backButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 25,
    width: 50,
    height: 50,
    alignItems: "center",
    justifyContent: "center",
  },
  headerTitle: {
    flex: 1,
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    marginHorizontal: 16,
  },
  headerSpacer: {
    width: 50,
  },
  content: {
    flex: 1,
  },
  profileContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  avatarSection: {
    alignItems: "center",
    marginBottom: 40,
  },
  avatarContainer: {
    position: "relative",
    marginBottom: 12,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 4,
    borderColor: "rgba(255, 255, 255, 0.3)",
  },
  avatarEditIcon: {
    position: "absolute",
    bottom: 8,
    right: 8,
    backgroundColor: "#007AFF",
    borderRadius: 16,
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "#fff",
  },
  avatarText: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.8)",
    textAlign: "center",
  },
  formContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.95)",
    borderRadius: 20,
    padding: 24,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  form: {
    gap: 20,
  },
  emailContainer: {
    marginTop: 8,
  },
  emailLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  emailField: {
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  emailText: {
    fontSize: 16,
    color: "#333",
    marginBottom: 4,
  },
  emailNote: {
    fontSize: 12,
    color: "#666",
    fontStyle: "italic",
  },
  currencySection: {
    marginTop: 8,
  },
  currencyLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  currencyContainer: {
    backgroundColor: "#f8f9fa",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#e1e5e9",
  },
  currencyValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
    marginBottom: 4,
  },
  currencyNote: {
    fontSize: 12,
    color: "#666",
    fontStyle: "italic",
  },
  updateButton: {
    marginTop: 8,
  },
  logoutSection: {
    marginTop: 16,
  },
  logoutButton: {
    borderColor: "rgba(255, 255, 255, 0.8)",
  },
})
