"use client"

import React, { useState, useRef, useCallback } from "react"
import { View, Text, StyleSheet, Dimensions, FlatList, type ViewToken, Platform } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { Button } from "../components/Button"
import type { StackNavigationProp } from "@react-navigation/stack"
import type { RootStackParamList } from "../types"

type OnboardingScreenNavigationProp = StackNavigationProp<RootStackParamList, "Onboarding">

interface Props {
  navigation: OnboardingScreenNavigationProp
}

const onboardingData = [
  {
    emoji: "📱",
    title: "Scan Receipts",
    description: "Simply take a photo of your receipt and let our AI extract all the items automatically.",
  },
  {
    emoji: "👥",
    title: "Split with Friends",
    description: "Create groups and split bills fairly among friends, roommates, or colleagues.",
  },
  {
    emoji: "💳",
    title: "Easy Payments",
    description: "Send and receive payments instantly through multiple payment methods.",
  },
]

const OnboardingScreen: React.FC<Props> = ({ navigation }) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const flatListRef = useRef<FlatList>(null)

  const handleNext = () => {
    console.log('Next button clicked, current index:', currentIndex)
    const nextIndex = currentIndex + 1
    if (nextIndex < onboardingData.length) {
      console.log('Moving to next slide:', nextIndex)
      if (Platform.OS === 'web') {
        setCurrentIndex(nextIndex)
      } else {
        flatListRef.current?.scrollToIndex({ index: nextIndex, animated: true })
      }
    } else {
      console.log('Navigating to Login')
      navigation.navigate("Login")
    }
  }

  const handleSkip = () => {
    console.log('Skip button clicked')
    navigation.navigate("Login")
  }

  const onViewableItemsChanged = useCallback(({ viewableItems }: { viewableItems: ViewToken[] }) => {
    if (viewableItems.length > 0 && viewableItems[0].index !== null) {
      setCurrentIndex(viewableItems[0].index)
    }
  }, [])

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  }

  return (
    <LinearGradient colors={["#007AFF", "#5856D6"]} style={styles.container}>
      {Platform.OS === 'web' ? (
        // Web version - simple slide display
        <View style={styles.slide}>
          <Text style={styles.emoji}>{onboardingData[currentIndex].emoji}</Text>
          <Text style={styles.title}>{onboardingData[currentIndex].title}</Text>
          <Text style={styles.description}>{onboardingData[currentIndex].description}</Text>
        </View>
      ) : (
        // Mobile version - FlatList
        <FlatList
          ref={flatListRef}
          data={onboardingData}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          keyExtractor={item => item.title}
          renderItem={({ item }) => (
            <View style={styles.slide}>
              <Text style={styles.emoji}>{item.emoji}</Text>
              <Text style={styles.title}>{item.title}</Text>
              <Text style={styles.description}>{item.description}</Text>
            </View>
          )}
        />
      )}

      <View style={styles.footer}>
        <View style={styles.pagination}>
          {onboardingData.map((_, index) => (
            <View
              key={index}
              style={[styles.dot, index === currentIndex && styles.activeDot]}
            />
          ))}
        </View>

        <View style={styles.buttons}>
          <Button
            title="Skip"
            onPress={handleSkip}
            variant="outline"
            size="medium"
          />
          <Button
            title={currentIndex === onboardingData.length - 1 ? "Get Started" : "Next"}
            onPress={handleNext}
            variant="secondary"
            size="medium"
          />
        </View>
      </View>
    </LinearGradient>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  slide: {
    ...(Platform.OS !== 'web' && { width: Dimensions.get('window').width }),
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Platform.OS === 'web' ? 60 : 40,
  },
  emoji: {
    fontSize: Platform.OS === 'web' ? 120 : 100,
    marginBottom: Platform.OS === 'web' ? 50 : 40,
  },
  title: {
    fontSize: Platform.OS === 'web' ? 32 : 28,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    marginBottom: 20,
  },
  description: {
    fontSize: Platform.OS === 'web' ? 20 : 18,
    color: "rgba(255, 255, 255, 0.8)",
    textAlign: "center",
    lineHeight: Platform.OS === 'web' ? 28 : 24,
    maxWidth: Platform.OS === 'web' ? 600 : undefined,
  },
  footer: {
    paddingHorizontal: Platform.OS === 'web' ? 60 : 40,
    paddingBottom: Platform.OS === 'web' ? 50 : 40,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'transparent',
  },
  pagination: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 30,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: "#fff",
  },
  buttons: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: 'center',
    minHeight: 50,
  },
})

export default OnboardingScreen
