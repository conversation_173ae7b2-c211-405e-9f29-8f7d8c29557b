"use client"

import type React from "react"
import { useState, useCallback } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, SafeAreaView, RefreshControl } from "react-native"
import { useNavigation, useRoute, useFocusEffect } from "@react-navigation/native"
import type { StackNavigationProp, RouteProp } from "@react-navigation/stack"
import type { RootStackParamList, Group, GroupMember, Bill } from "../types"
import { useAuth } from "../contexts/AuthProvider"
import { supabase } from "../../backend/supabase/client"
import Button from "../components/Button"
import { Ionicons } from "@expo/vector-icons"
import { formatDate } from "../utils/formatters"

type GroupDetailsScreenNavigationProp = StackNavigationProp<RootStackParamList, "GroupDetails">
type GroupDetailsScreenRouteProp = RouteProp<RootStackParamList, "GroupDetails">

const GroupDetailsScreen: React.FC = () => {
  const navigation = useNavigation<GroupDetailsScreenNavigationProp>()
  const route = useRoute<GroupDetailsScreenRouteProp>()
  const { user } = useAuth()
  const { groupId } = route.params

  const [group, setGroup] = useState<Group | null>(null)
  const [members, setMembers] = useState<GroupMember[]>([])
  const [bills, setBills] = useState<Bill[]>([])
  const [currentUserRole, setCurrentUserRole] = useState<"admin" | "member" | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchGroupDetails = async () => {
    if (!user) return

    try {
      // Fetch group details
      const { data: groupData, error: groupError } = await supabase
        .from("groups")
        .select("*")
        .eq("id", groupId)
        .single()

      if (groupError) throw groupError
      setGroup(groupData)

      // Fetch members with user details
      const { data: membersData, error: membersError } = await supabase
        .from("group_members")
        .select(`
          *,
          user:users(id, display_name, email, avatar_url)
        `)
        .eq("group_id", groupId)
        .order("joined_at", { ascending: true })

      if (membersError) throw membersError
      setMembers(membersData || [])

      // Find current user's role
      const currentMember = membersData?.find((member) => member.user_id === user.id)
      setCurrentUserRole(currentMember?.role || null)

      // Fetch recent bills
      const { data: billsData, error: billsError } = await supabase
        .from("bills")
        .select(`
          *,
          creator:users!bills_created_by_fkey(display_name)
        `)
        .eq("group_id", groupId)
        .order("created_at", { ascending: false })
        .limit(5)

      if (billsError) throw billsError
      setBills(billsData || [])
    } catch (error) {
      console.error("Error fetching group details:", error)
      Alert.alert("Error", "Failed to load group details")
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useFocusEffect(
    useCallback(() => {
      fetchGroupDetails()
    }, [user, groupId]),
  )

  const onRefresh = () => {
    setRefreshing(true)
    fetchGroupDetails()
  }

  const handleLeaveGroup = () => {
    Alert.alert(
      "Leave Group",
      "Are you sure you want to leave this group? You will lose access to all bills and chat history.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Leave",
          style: "destructive",
          onPress: async () => {
            try {
              const { error } = await supabase
                .from("group_members")
                .delete()
                .eq("group_id", groupId)
                .eq("user_id", user?.id)

              if (error) throw error

              Alert.alert("Left Group", "You have successfully left the group.", [
                { text: "OK", onPress: () => navigation.replace("Dashboard") },
              ])
            } catch (error) {
              console.error("Error leaving group:", error)
              Alert.alert("Error", "Failed to leave group. Please try again.")
            }
          },
        },
      ],
    )
  }

  const handleDeleteGroup = () => {
    Alert.alert(
      "Delete Group",
      "Are you sure you want to delete this group? This action cannot be undone and will remove all bills, payments, and chat history.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              const { error } = await supabase.from("groups").delete().eq("id", groupId)

              if (error) throw error

              Alert.alert("Group Deleted", "The group has been successfully deleted.", [
                { text: "OK", onPress: () => navigation.replace("Dashboard") },
              ])
            } catch (error) {
              console.error("Error deleting group:", error)
              Alert.alert("Error", "Failed to delete group. Please try again.")
            }
          },
        },
      ],
    )
  }

  const copyInviteCode = () => {
    if (group) {
      // In a real app, you would use Clipboard API
      Alert.alert("Invite Code", `Group invite code: ${group.invite_code}`, [{ text: "OK" }])
    }
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </SafeAreaView>
    )
  }

  if (!group) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Group not found</Text>
          <Button title="Go Back" onPress={() => navigation.goBack()} variant="primary" />
        </View>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Group Details</Text>
        <TouchableOpacity onPress={() => navigation.navigate("Chat", { groupId })} style={styles.chatButton}>
          <Ionicons name="chatbubble-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        {/* Group Info */}
        <View style={styles.section}>
          <View style={styles.groupHeader}>
            <View style={styles.groupIcon}>
              <Ionicons name="people" size={32} color="#007AFF" />
            </View>
            <View style={styles.groupInfo}>
              <Text style={styles.groupName}>{group.name}</Text>
              {group.description && <Text style={styles.groupDescription}>{group.description}</Text>}
              <Text style={styles.groupDate}>Created {formatDate(group.created_at)}</Text>
            </View>
          </View>

          <TouchableOpacity style={styles.inviteContainer} onPress={copyInviteCode}>
            <View style={styles.inviteInfo}>
              <Text style={styles.inviteLabel}>Invite Code</Text>
              <Text style={styles.inviteCode}>{group.invite_code}</Text>
            </View>
            <Ionicons name="copy-outline" size={20} color="#007AFF" />
          </TouchableOpacity>
        </View>

        {/* Members */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Members ({members.length})</Text>
          </View>
          {members.map((member) => (
            <View key={member.id} style={styles.memberCard}>
              <View style={styles.memberInfo}>
                <View style={styles.memberAvatar}>
                  <Text style={styles.memberAvatarText}>
                    {member.user?.display_name?.charAt(0).toUpperCase() || "?"}
                  </Text>
                </View>
                <View style={styles.memberDetails}>
                  <Text style={styles.memberName}>{member.user?.display_name || "Unknown"}</Text>
                  <Text style={styles.memberEmail}>{member.user?.email || ""}</Text>
                </View>
              </View>
              <View style={styles.memberRole}>
                <Text style={[styles.roleText, member.role === "admin" && styles.adminRoleText]}>{member.role}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Recent Bills */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Bills</Text>
            <TouchableOpacity onPress={() => navigation.navigate("History")}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          {bills.length > 0 ? (
            bills.map((bill) => (
              <TouchableOpacity
                key={bill.id}
                style={styles.billCard}
                onPress={() => navigation.navigate("BillEdit", { billId: bill.id })}
              >
                <View style={styles.billHeader}>
                  <Text style={styles.billTitle}>{bill.title}</Text>
                  <Text style={styles.billAmount}>${bill.total_amount.toFixed(2)}</Text>
                </View>
                <View style={styles.billFooter}>
                  <Text style={styles.billCreator}>by {bill.creator?.display_name}</Text>
                  <Text style={[styles.billStatus, { color: bill.status === "settled" ? "#34C759" : "#FF9500" }]}>
                    {bill.status}
                  </Text>
                </View>
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>No bills yet</Text>
              <Text style={styles.emptyStateSubtext}>Add a bill to start tracking expenses</Text>
            </View>
          )}
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <Button
            title="Add Bill"
            onPress={() => navigation.navigate("AddBill", { groupId })}
            variant="primary"
            style={styles.actionButton}
          />

          <Button
            title="View Chat"
            onPress={() => navigation.navigate("Chat", { groupId })}
            variant="secondary"
            style={styles.actionButton}
          />

          {currentUserRole === "admin" ? (
            <Button title="Delete Group" onPress={handleDeleteGroup} variant="danger" style={styles.actionButton} />
          ) : (
            <Button title="Leave Group" onPress={handleLeaveGroup} variant="outline" style={styles.actionButton} />
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F2F2F7",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: "#FFFFFF",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5EA",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1C1C1E",
  },
  chatButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  errorText: {
    fontSize: 18,
    color: "#FF3B30",
    marginBottom: 20,
  },
  section: {
    backgroundColor: "#FFFFFF",
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1C1C1E",
  },
  seeAllText: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "500",
  },
  groupHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  groupIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#F0F8FF",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#1C1C1E",
    marginBottom: 4,
  },
  groupDescription: {
    fontSize: 14,
    color: "#8E8E93",
    marginBottom: 4,
  },
  groupDate: {
    fontSize: 12,
    color: "#8E8E93",
  },
  inviteContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#F8F9FA",
    padding: 12,
    borderRadius: 8,
  },
  inviteInfo: {
    flex: 1,
  },
  inviteLabel: {
    fontSize: 12,
    color: "#8E8E93",
    marginBottom: 2,
  },
  inviteCode: {
    fontSize: 16,
    fontFamily: "monospace",
    fontWeight: "600",
    color: "#007AFF",
  },
  memberCard: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F2F2F7",
  },
  memberInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#007AFF",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  memberAvatarText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  memberDetails: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1C1C1E",
    marginBottom: 2,
  },
  memberEmail: {
    fontSize: 14,
    color: "#8E8E93",
  },
  memberRole: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: "#F2F2F7",
  },
  roleText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#8E8E93",
    textTransform: "capitalize",
  },
  adminRoleText: {
    color: "#007AFF",
  },
  billCard: {
    backgroundColor: "#F8F9FA",
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  billHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  billTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#1C1C1E",
  },
  billAmount: {
    fontSize: 16,
    fontWeight: "600",
    color: "#007AFF",
  },
  billFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  billCreator: {
    fontSize: 12,
    color: "#8E8E93",
  },
  billStatus: {
    fontSize: 12,
    fontWeight: "500",
    textTransform: "capitalize",
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 20,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#8E8E93",
    marginBottom: 4,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: "#8E8E93",
  },
  actionButton: {
    marginBottom: 12,
  },
})

export default GroupDetailsScreen
