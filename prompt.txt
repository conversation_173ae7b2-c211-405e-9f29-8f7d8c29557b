1. SplashScreen

Purpose:  
- Initial app entry; checks user’s authentication status.

How it should function:
- Shows app logo and loading indicator.
- Triggers async check: Is the user already authenticated/signed in (session token stored)?
- Navigates:
  - If authenticated → Dashboard.
  - If not → Onboarding or Login.

 2. OnboardingScreen

Purpose:  
- Briefly introduces app features with swipeable info cards.

How it should function:
- Renders a horizontally swipeable set of cards (e.g., using FlatList or a swiper library), one for each feature (scan, split, track).
- Visually highlights benefits (icons/text/images).
- “Get Started”/“Continue” button visible on last card.
- On tap: navigates to LoginScreen.

 3. LoginScreen / SignupScreen / ForgotPasswordScreen

Purpose:  
- User authentication and initial profile creation.

How they function:
- Each page has a form with real-time validation (email format, password rules).
- LoginScreen:
  - On login, verifies email/password via Supabase Auth.
  - If first login, checks if profile exists in DB, otherwise creates default.
  - Handles login errors and displays reasons (e.g., wrong password).
- SignupScreen:
  - On submit, creates user via Supabase Auth, sends verification email.
  - Stores basic profile in `users` table (id from auth, display name/email).
- ForgotPasswordScreen:
  - Requests a reset email via Supabase.
  - Provides feedback (sent, or error).

Session Persistence:
- On successful login/signup, stores session securely (async storage).
- Handles session renewal across app restarts.

 4. DashboardScreen

Purpose:  
- App home; quick view of all user groups and recent bill activity.

Functionality:
- Fetches from Supabase in real-time:
  - Groups where current user is a member.
  - Most recent bills in those groups (show summary below each group).
- UI:
  - List of groups as cards, each with:
    - Name, balance summary, and latest bill info.
    - Tap on card → navigates to GroupDetailsScreen for that group.
  - Action buttons:
    - “Create Group” → CreateGroupScreen
    - “Join Group” → JoinGroupScreen
    - “View History” → HistoryScreen
  - Avatar icon (top-right) → ProfileScreen
  - Notifications bell → NotificationsScreen

 5. CreateGroupScreen

Purpose:  
- Allows user to create a new group and add members.

Functionality:
- Form with:
  - Group name (required), description (optional).
  - Add members by searching other users (fetch with Supabase function).
  - Accepts email(s) or username(s); live search with autocomplete.
  - Press “Create”: 
    - Inserts new group in `groups` table.
    - Inserts into `group_members` table (current user as first member + added members).
    - On success, navigates back to Dashboard with new group.
- Shows confirmations/errors for all submission states.

 6. JoinGroupScreen (if implemented)

Purpose:  
- Lets user join existing groups by code or search.

Functionality:
- Form to input invite code (short unique group identifier).
  - On submit, fetches group by code.
  - If found: adds user to `group_members` table (if not already present).
  - If not: shows error.
- (Alternately) displays search field to find open/joinable groups.
- On successful join, navigates to GroupDetailsScreen.

 7. GroupListScreen

Purpose:  
- Shows all groups user is a member of (alternative to Dashboard if separated).

Functionality:
- Fetches and lists groups from Supabase using real-time subscriptions.
- Tap on group → GroupDetailsScreen.

 8. GroupDetailsScreen

Purpose:  
- Displays group-specific data: members, balances, and bills.

Functionality:
- Shows:
  - Group name and info.
  - List of member avatars and names (queried via `group_members` join).
  - Visual balances: Who owes/gets, calculated from open/settled bills.
  - List of bills for this group (latest first).
    - Tap on bill → opens BillEditScreen/BillSplitScreen for viewing/editing.
  - “+ Add Bill” button → AddBillScreen prefilled with this group.
  - “Chat” → ChatScreen for this group.
- Live updates:
  - Subscribes to Supabase for new bills, payments, and chat messages.

 9. AddBillScreen

Purpose:  
- User adds a new bill by scanning or uploading a receipt.

Functionality:
- User options:
  - “Take Photo” (camera) or “Upload Image” (gallery).
  - Chooses image and previews it.
- On confirm:
  - Uploads image to Supabase Storage (`bills` bucket).
  - Calls OCR service (Tesseract.js/ML Kit) to parse text in background.
  - Receives detected text/items, stores in raw form in DB (`bills` + `bill_items` tables).
  - Navigates to BillEditScreen with parsed data.

 10. BillEditScreen

Purpose:  
- Displays and edits detected bill items from OCR; assigns items to members.

Functionality:
- Shows editable list for each parsed item:
  - Name (editable)
  - Price (numeric, required)
  - Assigned members: per item, multi-select from group (chips or badge toggles)
- UI to add/remove more items (user adds missing lines or deletes OCR errors).
- Inputs for extras:
  - Add tip/tax/other (amount or percentage; automatically recalculated in total).
- Shows calculated total amount (items + extras).
- “Save & Split” button: saves all edits to DB, navigates to BillSplitScreen.

 11. BillSplitScreen

Purpose:  
- User selects how the bill is split and reviews results before confirming.

Functionality:
- Choose split mode:
  - Equal split (across group)
  - By item (based on item assignments—each member pays for their own)
  - Custom split (manually adjust share per member, by percentage/amount)
- Shows a table/view of:
  - Each member, amount owed, how calculated.
- Real-time updates as user adjusts split.
- “Confirm Split” button: 
  - Saves split assignments/amounts to DB (`bill_items` and `payments` tables).
  - Triggers notifications to relevant members (“You owe X in group Y”).
  - Navigates to PaymentScreen for current user to settle their share.

 12. PaymentScreen

Purpose:  
- Allows users to pay or mark items as paid.

Functionality:
- Loads all pending payments for current user (filtered from `payments`).
- UI lists each bill/group and amount.
  - “Settle Up” button: 
    - For now, marks payment as “paid” in DB on confirmation (payment gateway stub).
    - Optionally provides UPI/Stripe/PayPal integration—can be added with a wrapper service.
  - Updates payment record and triggers real-time status/balance updates.
  - Shows receipt or confirmation.
- Displays clear distinction between paid and pending.

 13. HistoryScreen

Purpose:  
- Displays expense history for the user and across groups.

Functionality:
- Filters for:
  - All expenses, by group, by date (month/year).
- Each entry:
  - Bill title, total, date, group, user’s share, paid/unpaid.
- Option to export data (CSV/PDF)—either calls a local export or describes where provided.
- Shows analytics visuals (trend chart, pie chart using Victory Native/chart library).

 14. ProfileScreen

Purpose:  
- User profile management, including avatar and preferences.

Functionality:
- Shows current:
  - Display name, email, profile photo, currency choice.
  - Buttons to edit display name, update password, upload/change avatar (uploads to Supabase Storage).
- Save updates to Supabase `users` table; uses Storage for image.
- “Logout” button triggers sign-out in AuthContext and navigates to Login.

 15. NotificationsScreen

Purpose:  
- Inbox for all app notifications.

Functionality:
- Lists notification messages:
  - New bill created
  - Payment reminders
  - Status changes (payment marked as paid)
- Real-time: Subscribes to notifications with Supabase/Expo Push architecture.
- Marks messages as read, updates badge count.
- Tapping notification can deep-link to related group or bill.

 16. ChatScreen 

Purpose:  
- Real-time group chat.

Functionality:
- Message list, sent/received by group members.
- Sends new message: inserts to `chats` with user and group id.
- Receives new messages instantly with Supabase Realtime.
- UI handles emoji, reply-to threading (if desired).
- Member avatars and timestamps.

flow:
SplashScreen.tsx:

Displays app logo and loading spinner.

Checks user auth session on app load.

OnboardingScreen.tsx:

Swipeable cards for feature introduction.

“Get Started” button proceeds to login/signup.

LoginScreen/SignupScreen.tsx:

Forms validated for email format and password security.

Fire Supabase Auth API calls.

Show error/success messages.

DashboardScreen.tsx:

Fetches groups user belongs to.

Displays recent bills with quick summaries.

Navigation buttons for core actions.

GroupDetailsScreen.tsx:

Shows members with avatars.

Lists bills linked to group.

Interactive UI for adding bills, viewing balances.

AddBillScreen.tsx:

Open camera/gallery.

Upload image and trigger OCR parsing.

Navigate to EditBillItems screen.

BillEditScreen.tsx:

Editable list of items with name, price, assigned users.

Input for tip/tax and auto-calculate total.

Save changes to backend.

BillSplitScreen.tsx:

UI for split mode selection.

Preview per-user owed amounts.

Confirm and persist splits.

PaymentScreen.tsx:

Show debts and allow payment or marking as paid.

Support payment gateway stubs.