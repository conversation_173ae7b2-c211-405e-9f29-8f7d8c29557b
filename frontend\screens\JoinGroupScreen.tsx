"use client"

import type React from "react"
import { useState } from "react"
import { View, Text, StyleSheet, ScrollView, Alert } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { Button } from "../components/Button"
import { InputField } from "../components/InputField"
import { useAuth } from "../contexts/AuthProvider"
import { supabase } from "../services/supabase"
import type { StackNavigationProp } from "@react-navigation/stack"
import type { RootStackParamList } from "../types"

type JoinGroupScreenNavigationProp = StackNavigationProp<RootStackParamList, "JoinGroup">

interface Props {
  navigation: JoinGroupScreenNavigationProp
}

const JoinGroupScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuth()
  const [inviteCode, setInviteCode] = useState("")
  const [loading, setLoading] = useState(false)

  const handleJoinGroup = async () => {
    if (!inviteCode.trim()) {
      Alert.alert("Error", "Please enter an invite code")
      return
    }

    if (!user) return

    setLoading(true)
    try {
      // Find group by invite code
      const { data: groupData, error: groupError } = await supabase
        .from("groups")
        .select("*")
        .eq("invite_code", inviteCode.trim())
        .single()

      if (groupError || !groupData) {
        Alert.alert("Error", "Invalid invite code. Please check and try again.")
        return
      }

      // Check if user is already a member
      const { data: existingMember, error: memberCheckError } = await supabase
        .from("group_members")
        .select("id")
        .eq("group_id", groupData.id)
        .eq("user_id", user.id)
        .single()

      if (memberCheckError && memberCheckError.code !== "PGRST116") {
        throw memberCheckError
      }

      if (existingMember) {
        Alert.alert("Already a Member", "You are already a member of this group.")
        navigation.navigate("GroupDetails", { groupId: groupData.id })
        return
      }

      // Add user to group
      const { error: joinError } = await supabase.from("group_members").insert([
        {
          group_id: groupData.id,
          user_id: user.id,
          role: "member",
        },
      ])

      if (joinError) throw joinError

      Alert.alert("Success", `You've joined "${groupData.name}"!`, [
        {
          text: "OK",
          onPress: () => navigation.navigate("GroupDetails", { groupId: groupData.id }),
        },
      ])
    } catch (error) {
      console.error("Error joining group:", error)
      Alert.alert("Error", "Failed to join group. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Button title="Cancel" onPress={() => navigation.goBack()} variant="outline" size="small" />
        <Text style={styles.title}>Join Group</Text>
        <View style={{ width: 60 }} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.form}>
          <View style={styles.illustration}>
            <Text style={styles.emoji}>🎉</Text>
            <Text style={styles.illustrationTitle}>Join a Group</Text>
            <Text style={styles.illustrationText}>
              Enter the invite code shared by a group member to join their group
            </Text>
          </View>

          <InputField
            label="Invite Code"
            placeholder="Enter invite code"
            value={inviteCode}
            onChangeText={setInviteCode}
          />

          <Button title="Join Group" onPress={handleJoinGroup} loading={loading} size="large" />

          <View style={styles.info}>
            <Text style={styles.infoTitle}>💡 How to get an invite code:</Text>
            <Text style={styles.infoText}>• Ask a group member to share the invite code</Text>
            <Text style={styles.infoText}>• Group admins can find the code in group settings</Text>
            <Text style={styles.infoText}>• Codes are unique for each group</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  form: {
    paddingTop: 24,
    gap: 20,
  },
  illustration: {
    alignItems: "center",
    paddingVertical: 40,
  },
  emoji: {
    fontSize: 60,
    marginBottom: 16,
  },
  illustrationTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  illustrationText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
  },
  info: {
    backgroundColor: "#f8f9fa",
    padding: 16,
    borderRadius: 12,
    marginTop: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
    lineHeight: 20,
  },
})

export default JoinGroupScreen
